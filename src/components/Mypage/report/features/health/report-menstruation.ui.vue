<template>
  <main class="menstruation-section">
    <h1 class="title">우먼</h1>

    <section class="section-card">
      <p class="card-text">2월 생리 주기는 28일 입니다.</p>
      <ProgressBar
        :currentValue="28"
        :averageValue="30"
        unit="일"
        labelCurrent="2월 주기"
        labelAverage="평균주기"
      />
    </section>

    <hr class="divider" />

    <section class="section-card">
      <p class="card-text">
        2월 생리 기간은 2월 16일~2월 21일로 총 6일 입니다.
      </p>
      <ProgressBar
        :currentValue="6"
        :averageValue="7"
        unit="일"
        labelCurrent="2월 기간"
        labelAverage="평균기간"
      />
    </section>

    <footer class="info-box">
      <p>
        다음 생리 예상일은 3월 18일 입니다. 생리 기간에는 소변검사를 피해
        주세요.
      </p>
    </footer>
  </main>
</template>

<script>
import ProgressBar from "../../ui/woman-menstruation-bar.ui.vue";

export default {
  name: "MenstrualCycleTracker",
  components: {
    ProgressBar,
  },
};
</script>

<style lang="scss" scoped>
.menstruation-section {
  width: 100% !important;
  padding: 30px;
  display: flex;
  flex-direction: column;
  align-content: flex-start;

  > section > nav {
    display: flex;
    justify-content: center;
  }
}

.title {
  font-size: 1.5rem; /* Equivalent to text-2xl */
  font-weight: bold;
  margin-bottom: 1.5rem; /* Equivalent to mb-6 */
  text-align: start;
}

.divider {
  margin: 30px 0;
  border: 2px solid #ededed;
  border-radius: 10px;
}

.info-box {
  margin-top: 30px;
  padding: 20px 30px; /* Equivalent to p-4 */
  background-color: #f8f8f8; /* Equivalent to bg-gray-100 */
  border-radius: 10px; /* Equivalent to rounded-lg */
  font-size: 14px; /* Equivalent to text-base */
  line-height: 1.625; /* Equivalent to leading-relaxed */
  color: #000000;
  > p {
    margin: 0;
  }
}

.card-text {
  width: 100%;
  font-size: 14px;
  line-height: 24px;
  letter-spacing: -0.03em;
  color: #000000;
  font-weight: 500;
  text-align: left;
  margin-bottom: 30px;
}
</style>
