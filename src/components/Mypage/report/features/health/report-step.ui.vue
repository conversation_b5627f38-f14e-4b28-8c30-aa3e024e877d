<template>
  <main class="step-section">
    <header class="step-header">
      <h3 class="step-title">걸음</h3>
      <span>총 {{ totalDaysRecord }}일 기록</span>
    </header>
    <section class="step-values">
      <p class="step-summary">
        한달 평균 하루 걸음수는
        {{ new Intl.NumberFormat().format(weeklyAverages.avgSteps) }}보 입니다.
        이번달 전체 걸음수는 총 {{ totalStepData }}보 입니다.
      </p>
      <StepChart :chartData="chartDataForSteps" />
    </section>
    <footer class="step-guide">
      <p class="step-guide-text">
        목표 걸음수는 {{ new Intl.NumberFormat().format(targetStep) }}보 입니다.
        {{ stepGoalText }}
      </p>
    </footer>
  </main>
</template>

<script>
import StepChart from "../../ui/step-chart.ui";

import {
  groupByWeek,
  calculateWeeklyAverages,
  formatForChart,
} from "./utils/step.utils";

import { STEP_SITUATIONS } from "./constants/step.enum";

export default {
  name: "ReportStep",

  components: {
    StepChart,
  },

  props: {
    stepData: {
      type: Array,
      required: true,
      default: () => [],
    },
    selectedDate: {
      type: String,
      required: true,
      default: () => {
        const date = new Date();
        return `${date.getFullYear()}-${date.getMonth() + 1}`;
      },
    },

    targetStep: {
      type: Number,
      required: true,
      default: 0,
    },
  },

  data() {
    return {};
  },

  computed: {
    totalDaysRecord() {
      if (this.stepData.length === 0) return 0;

      const date = this.selectedDate;
      const [year, month] = date.split("-").map(Number);
      const lastDay = new Date(year, month, 0).getDate();

      return lastDay;
    },

    hasMonthlyStepData() {
      if (this.stepData.length > 0) {
        return STEP_SITUATIONS.HAS_DATA;
      }

      return STEP_SITUATIONS.NO_DATA;
    },

    // 주차별 데이터 (props가 변경될 때마다 자동으로 재계산)
    weeklyStepData() {
      if (!this.stepData || this.stepData.length === 0) return [];

      try {
        return groupByWeek(this.stepData);
      } catch (error) {
        console.error("주차별 데이터 처리 중 오류:", error);
        return [];
      }
    },

    totalStepData() {
      const steps = groupByWeek(this.stepData);

      if (steps.length > 0) {
        return new Intl.NumberFormat().format(
          steps.reduce((acc, week) => acc + week.totalSteps, 0)
        );
      }

      return 0;
    },

    // 주차별 평균 계산
    weeklyAverages() {
      if (this.weeklyStepData.length === 0) {
        return { avgSteps: 0, avgDistance: 0, avgCalories: 0 };
      }

      return calculateWeeklyAverages(this.weeklyStepData);
    },

    // 차트용 데이터 (걸음수 기준)
    chartDataForSteps() {
      if (this.weeklyStepData.length === 0) return [];

      return formatForChart(this.weeklyStepData, "steps");
    },

    stepGoalText() {
      const steps = groupByWeek(this.stepData);

      const sum = steps.reduce((acc, week) => acc + week.totalSteps, 0) ?? 0;

      if (sum >= this.targetStep) {
        return "목표를 달성했습니다! 👏🏻​";
      }

      const diff = this.targetStep - sum;

      return `목표까지 걸음수를 ${new Intl.NumberFormat().format(
        diff
      )}보 늘려야합니다.`;
    },
  },

  // props 변경을 감지하고 추가 작업이 필요한 경우
  watch: {
    stepData: {
      handler(newData) {
        // 필요시 추가 작업 수행
        this.handleStepDataChange(newData);
      },
      immediate: true, // 컴포넌트 생성 시에도 실행
      deep: true, // 배열 내부 객체 변경도 감지
    },

    selectedDate: {
      handler(newDate) {
        console.log("선택된 날짜 변경됨:", newDate);
        // 날짜 변경 시 필요한 작업 수행
      },
      immediate: true,
    },

    // 주차별 데이터 변경을 감지
    weeklyStepData: {
      handler(newWeeklyData) {
        console.log("주차별 데이터 업데이트됨:", newWeeklyData);

        // 차트 라이브러리 업데이트나 다른 작업 수행
        this.$nextTick(() => {
          // DOM 업데이트 후 실행할 작업
          this.updateCharts();
        });
      },
    },
  },

  methods: {
    handleStepDataChange(newData) {
      // stepData가 변경될 때 수행할 작업
      if (!newData || newData.length === 0) {
        console.log("데이터가 없습니다.");
        return;
      }

      // 예: 데이터 검증
      const validData = this.validateStepData(newData);
      if (!validData) {
        console.warn("잘못된 데이터 형식입니다.");
        return;
      }

      console.log(`${newData.length}개의 걸음 데이터가 로드되었습니다.`);
    },

    validateStepData(data) {
      // 방법 1: in 연산자 사용 (가장 간단)
      return data.every((item) => {
        return (
          "date" in item &&
          "totalStepCount" in item &&
          "totalDistance" in item &&
          "totalCalories" in item
        );
      });
    },

    updateCharts() {
      // 차트 업데이트 로직
      // 예: Chart.js나 다른 차트 라이브러리 업데이트
      console.log("차트 업데이트 중...");
    },

    // 특정 메트릭의 차트 데이터를 가져오는 메서드
    getChartData(metric = "steps") {
      if (this.weeklyStepData.length === 0) return [];

      return formatForChart(this.weeklyStepData, metric);
    },
  },

  // 컴포넌트가 마운트된 후 실행
  mounted() {},
};
</script>

<style lang="scss" scoped>
.step-section {
  width: 100% !important;
  padding: 30px;
  display: flex;
  flex-direction: column;
  align-content: flex-start;

  > section > nav {
    display: flex;
    justify-content: center;
  }
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.step-values {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 20px;
}

.step-summary {
  font-size: 14px;
  font-weight: 500;
  color: #000000;
  text-align: start;
  letter-spacing: -3%;
}

.step-guide-text {
  font-size: 14px;
  font-weight: 500;
  color: #000000;
  line-height: 25px;
  letter-spacing: -3%;
  margin: 0;
}

.step-guide {
  width: 100%;
  background: #f8f8f8;
  padding: 30px 20px;
  border-radius: 10px;
}
</style>
