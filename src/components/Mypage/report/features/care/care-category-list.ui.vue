<template>
  <div class="category-list">
    <CareCategory
      v-for="category in CARE_CATEGORIES"
      :key="category.categoryKey"
      :categoryType="category.categoryType"
      :categoryValue="categoryValues[category.categoryKey]"
      :categoryUnit="category.categoryUnit"
      :categoryKey="category.categoryKey"
    />
  </div>
</template>

<script>
import CareCategory from "./care-category.ui.vue";

import { CARE_CATEGORIES } from "./constants/care-category.constants";

export default {
  name: "CareCategoryList",

  components: {
    CareCategory,
  },

  props: {
    selectedDate: {
      type: Date,
      required: true,
    },
    detailData: {
      type: Object,
      required: true,
      default: () => ({
        weight: "0",
        water: "0",
        pee: "0",
        bloodPressure: "0/0",
        step: "0",
        woman: "0",
      }),
    },
  },

  data() {
    return {
      CARE_CATEGORIES,
      categoryValues: {
        weight: "",
        water: "",
        pee: "",
        bloodPressure: "",
        step: "",
        woman: "",
      },
    };
  },

  methods: {
    checkDetailData(detail, key) {
      if (!detail) return;

      switch (key) {
        case "bloodPressure": {
          for (let i = 0, len = detail.length; i < len; i++) {
            const detailData = detail[i];

            if (
              detailData &&
              typeof detailData === "object" &&
              "systolic" in detailData &&
              "diastolic" in detailData
            ) {
              return `${detailData.systolic}/${detailData.diastolic}`;
            }
            return "0/0";
          }
          return "0/0";
        }
        case "step": {
          if (!detail) return "0";

          const steps = detail.totalStepCount;

          return steps.toLocaleString("ko-KR") ?? 0;
        }
        case "woman":
          return "";
        case "urine": {
          return String(detail.length) ?? "0";
        }
        default: {
          if (!detail) return "-";

          if (detail.length === 0) return "0";

          let sum = 0;

          // 데이터 규격이 똑같다고 가정. { id: number, value: number, createdAt: string }
          for (let i = 0, len = detail.length; i < len; i++) {
            const detailData = detail[i];

            if (
              detailData &&
              typeof detailData === "object" &&
              "value" in detailData
            ) {
              sum += Number(detailData.value) || 0;
            }
          }

          console.log(sum);

          return sum.toLocaleString("ko-KR");
        }
      }
    },
    init(newData) {
      Object.entries(newData).forEach(([key, value]) => {
        const detailData = this.checkDetailData(value, key);

        if (detailData) {
          if (key === "urine") {
            this.categoryValues["pee"] = detailData;
          } else {
            this.categoryValues[key] = detailData;
          }
        }
      });

      console.log(this.categoryValues);
    },
  },

  watch: {
    detailData: {
      handler(newData) {
        this.init(newData);
      },
      deep: true,
    },
  },
};
</script>

<style lang="scss" scoped>
.category-list {
  width: 100%;
  max-width: 450px;
  height: auto;
  padding: 30px;
  background: #ffffff;
}
</style>
