<template>
  <div class="category-list">
    <CareCategory
      v-for="category in CARE_CATEGORIES"
      :key="category.categoryKey"
      :categoryType="category.categoryType"
      :categoryValue="categoryValues[category.categoryKey]"
      :categoryUnit="category.categoryUnit"
      :categoryKey="category.categoryKey"
    />
  </div>
</template>

<script>
import CareCategory from "./care-category.ui.vue";

import { CARE_CATEGORIES } from "./constants/care-category.constants";

export default {
  name: "CareCategoryList",

  components: {
    CareCategory,
  },

  props: {
    selectedDate: {
      type: Date,
      required: true,
    },
    detailData: {
      type: Object,
      required: true,
      default: () => ({
        weight: "0",
        water: "0",
        pee: "0",
        bloodPressure: "0",
        step: "0",
        woman: "0",
      }),
    },
  },

  data() {
    return {
      CARE_CATEGORIES,
      categoryValues: {
        weight: "",
        water: "",
        pee: "",
        bloodPressure: "",
        step: "",
        woman: "",
      },
    };
  },

  methods: {
    checkDetailData(detail, key) {
      if (detail === null) {
        return "-";
      }

      switch (key) {
        case "bloodPressure":
          return "";
        case "step":
          return "";
        case "woman":
          return "";
        default: {
          if (!detail) return "-";

          if (detail.length === 0) return "0";

          let sum = 0;

          // 데이터 규격이 똑같다고 가정. { id: number, value: number, createdAt: string }
          for (let i = 0, len = detail.length; i < len; i++) {
            const detailData = detail;

            if ("value" in detailData) {
              sum += detailData.value;
            }
          }

          console.log(sum.toLocaleString());

          return sum.toLocaleString("ko-KR");
        }
      }
    },
  },

  watch: {
    detailData: {
      handler(newData) {
        Object.entries(newData).forEach(([key, value]) => {
          console.log([key, value]);
          const detailData = this.checkDetailData(value, key);

          this.categoryValues[key] = detailData;
        });
        
        console.log(this.categoryValues);
      },
      deep: true,
    },
  },
};
</script>

<style lang="scss" scoped>
.category-list {
  width: 100%;
  max-width: 450px;
  height: auto;
  padding: 30px;
  background: #ffffff;
}
</style>
