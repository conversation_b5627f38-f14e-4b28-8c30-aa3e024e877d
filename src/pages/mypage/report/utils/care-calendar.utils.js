/**
 *
 * @param {Array} data
 *
 */
export function progressCareCalendarCalculate(data) {
  const result = [];

  for (let i = 0, len = data.length; i < len; i++) {
    const { date, progress } = data[i];

    if (!date) break;

    result.push({
      date,
      progress,
    });
  }

  return result;
}

/**
 * @param {string} date
 * @param {Array} data
 */
export function detailCareCalendarData(date, data) {
  for (const dateData of data) {
    if (date === dateData.date) {
      const { progress, ...detail } = dateData;
      return { ...detail };
    }
  }
}
