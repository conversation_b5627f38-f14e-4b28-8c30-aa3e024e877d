<template>
  <section>
    <!-- calendar -->
    <CareCalendarTable
      :selectedDate="selectedDate"
      :calendarData="calendarData"
      @update:selectedDate="handleDateChange"
      @date-clicked="handleDateClicked"
    />

    <!-- categories -->
    <CategoryList :selectedDate="selectedDate" :detailData="detailData" />
  </section>
</template>

<script>
import CareCalendarTable from "@/components/Mypage/report/features/care/care-calendar.ui.vue";
import CategoryList from "@/components/Mypage/report/features/care/care-category-list.ui.vue";

import { reportApi } from "@/api/report";

import {
  progressCareCalendarCalculate,
  detailCareCalendarData,
} from "../utils/care-calendar.utils";

export default {
  name: "CareCalendar",

  components: {
    CareCalendarTable,
    CategoryList,
  },

  data() {
    return {
      selectedDate: new Date(),
      calendarData: [],
      isLoading: false,
      isRefreshing: false,
      error: null,
      retryCount: 0,
      maxRetries: 3,
      responseData: [],
      detailData: {},
    };
  },

  methods: {
    /**
     * 자식 컴포넌트에서 날짜 변경 이벤트를 받아 처리
     * @param {Date} newDate - 새로 선택된 날짜
     */
    async handleDateChange(newDate) {
      const oldDate = this.selectedDate;
      this.selectedDate = newDate;

      // 월이 변경된 경우 fetchCareCalendarData 호출
      if (
        !oldDate ||
        newDate.getFullYear() !== oldDate.getFullYear() ||
        newDate.getMonth() !== oldDate.getMonth()
      ) {
        const formattedDate = this.formatDateForApi(newDate);
        await this.fetchCareCalendarData(formattedDate, false);
      }
    },

    /**
     * 날짜 클릭 이벤트 처리
     * @param {Object} dateInfo - 클릭된 날짜의 상세 정보
     */
    handleDateClicked(dateInfo) {
      console.log("Date clicked:", dateInfo);

      // 여기서 클릭된 날짜의 추가 데이터를 활용할 수 있습니다
      if (dateInfo.hasProgress) {
        console.log(`Progress: ${dateInfo.progressValue}%`);
        console.log("Calendar data:", dateInfo.calendarData);
      }

      // 예: 특정 조건에 따른 추가 처리
      if (dateInfo.progressValue > 80) {
        console.log("High progress day!");
      }

      // 데이터를 컴포넌트 상태에 저장하고 싶다면
      // this.selectedDateInfo = dateInfo;
    },

    /**
     * 선택된 날짜에 해당하는 진행 데이터 로드
     * @param {Date} date - 로드할 날짜
     */
    async loadProgressDataForDate(date) {
      const formattedDate = this.formatDateForApi(date);
      await this.fetchCareCalendarData(formattedDate, false);
    },

    formatDate() {
      const date = this.selectedDate;
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      return `${year}-${month}`;
    },

    /**
     * API 호출용 날짜 포맷
     * @param {Date} date - 포맷할 날짜
     * @returns {string} YYYY-MM 형식의 날짜 문자열
     */
    formatDateForApi(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      return `${year}-${month}`;
    },

    /**
     * 케어 캘린더 데이터 가져오기 (재시도 로직 포함)
     * @param {string} date - YYYY-MM 형식의 날짜
     * @param {boolean} isInitial - 초기 로드 여부
     * @param {number} retryAttempt - 현재 재시도 횟수
     */
    async fetchCareCalendarData(date, isInitial = true, retryAttempt = 0) {
      try {
        // 로딩 상태 설정
        if (isInitial) {
          this.isLoading = true;
        } else {
          this.isRefreshing = true;
        }

        this.error = null;

        const subjectId = localStorage.subjectId;
        const isIos =
          navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
          navigator.userAgent.toLowerCase().indexOf("ipad") > -1;

        if (!subjectId) {
          throw new Error("Subject ID not found");
        }

        const response = await reportApi.getCareCalendar(subjectId, date);

        // 응답 데이터 검증
        if (!response || !response.data) {
          throw new Error("Invalid response data");
        }

        this.responseData = response.data;

        // 데이터 변환 및 설정
        const progressData = progressCareCalendarCalculate(
          response.data,
          isIos
        );
        this.calendarData = progressData ?? [];

        // 재시도 카운트 리셋
        this.retryCount = 0;
      } catch (error) {
        console.error(
          `Failed to load care calendar data (attempt ${retryAttempt + 1}):`,
          error
        );

        this.error = error.message || "Failed to load data";

        // 재시도 로직
        if (retryAttempt < this.maxRetries) {
          console.log(`Retrying... (${retryAttempt + 1}/${this.maxRetries})`);
          this.retryCount = retryAttempt + 1;

          // 지수 백오프: 1초, 2초, 4초 대기
          const delay = Math.pow(2, retryAttempt) * 1000;
          await this.sleep(delay);

          return this.fetchCareCalendarData(date, isInitial, retryAttempt + 1);
        } else {
          // 최대 재시도 횟수 초과
          this.retryCount = this.maxRetries;
          throw error;
        }
      } finally {
        this.isLoading = false;
        this.isRefreshing = false;
      }
    },

    /**
     * 수동 새로고침
     */
    async refreshData() {
      const date = this.formatDate();
      await this.fetchCareCalendarData(date, false);
    },

    /**
     * 특정 월의 데이터 로드
     * @param {number} year - 년도
     * @param {number} month - 월 (1-12)
     */
    async loadDataForMonth(year, month) {
      const date = `${year}-${String(month).padStart(2, "0")}`;
      await this.fetchCareCalendarData(date, false);
    },

    /**
     * 초기 데이터 로드
     */
    async loadInitialData() {
      const date = this.formatDate();
      await this.fetchCareCalendarData(date, true);
    },

    /**
     * 에러 상태 클리어
     */
    clearError() {
      this.error = null;
      this.retryCount = 0;
    },

    /**
     * 지연 함수
     * @param {number} ms - 대기 시간 (밀리초)
     */
    sleep(ms) {
      return new Promise((resolve) => setTimeout(resolve, ms));
    },
  },

  async mounted() {
    await this.loadInitialData();
  },

  watch: {
    selectedDate: {
      handler(newDate) {
        // 선택된 날짜가 변경될 때 추가 처리
        console.log("Selected date changed:", newDate);

        const date = new Date(newDate);

        const formatDate = `${date.getFullYear()}-${String(
          date.getMonth() + 1
        ).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;

        const detailData = detailCareCalendarData(
          formatDate,
          this.responseData
        );

        this.detailData = detailData;
      },
      deep: true,
    },
  },
};
</script>

<style lang="scss" scoped>
section {
  width: 100%;
  padding-bottom: 80px;
}
</style>
