<template>
  <section>
    <!-- 프로필 -->
    <HealthReportProfile
      :selectedDate="selectedDate"
      :profileData="profileData"
    />

    <DivideLine />

    <!-- 소변검사 리포트 데이터 -->
    <ReportUrineTest :urineData="urineData" />

    <DivideLine />

    <!--- 체중 리포트 -->
    <ReportWeight
      :targetWeight="weightData.targetWeight"
      :currentWeight="weightData.currentWeight"
      :bmi="weightData.bmi"
      :selectedDate="selectedDate"
    />

    <DivideLine />

    <!-- 수분 섭취량 리포트 -->
    <ReportWater
      :selectedDate="selectedDate"
      :averageWater="waterData.averageWater"
      :totalWater="waterData.totalWater"
      :targetWater="waterData.targetWater"
    />

    <DivideLine />

    <!-- 소변 횟수 리포트 -->
    <ReportUrineTimes
      :urinesTimes="urineTimesData.urineCount"
      :selectedDate="selectedDate"
    />

    <DivideLine />

    <!-- 혈압 리포트 -->
    <ReportBloodPressure
      :systolic="bloodPressureData.systolic"
      :diastolic="bloodPressureData.diastolic"
      :selectedDate="selectedDate"
    />

    <DivideLine />

    <!-- 걸음수 리포트 -->
    <ReportStep
      :selectedDate="selectedDate"
      :stepData="stepData.steps"
      :targetStep="stepData.targetStep"
    />

    <DivideLine />

    <!-- 여성 우먼(생리) 리포트 -->
    <ReportMenstruation :menstruationData="menstruationData" />

    <!-- modals-->

    <!-- 날짜 선택 모달 -->
    <SelectDateModal />

    <!-- BMI 정보 모달 -->
    <BmiInfoModal :bmi="weightData.bmi" />

    <!-- 혈압 범주 모달 -->
    <BloodPressureCategoryModal />
  </section>
</template>

<script>
import MockData from "./test.json";

import HealthReportProfile from "@/components/Mypage/report/features/health/health-report-profile.ui.vue";
import ReportUrineTest from "@/components/Mypage/report/features/health/report-urine-test.ui.vue";
import ReportWeight from "@/components/Mypage/report/features/health/report-weight.ui.vue";
import ReportWater from "@/components/Mypage/report/features/health/report-water.ui.vue";
import ReportUrineTimes from "@/components/Mypage/report/features/health/report-urine-time.ui.vue";
import ReportBloodPressure from "@/components/Mypage/report/features/health/report-blood-pressure.ui.vue";
import ReportStep from "@/components/Mypage/report/features/health/report-step.ui.vue";
import ReportMenstruation from "@/components/Mypage/report/features/health/report-menstruation.ui.vue";

import DivideLine from "@/components/Mypage/report/ui/divide.ui.vue";

import { getBloodPressureMinMax } from "../utils/blood-pressure-value.utils";

export default {
  name: "HealthReport",

  components: {
    HealthReportProfile,

    // 리포트 데이터 컴포넌트
    ReportUrineTest,

    // 체중 리포트 컴포넌트
    ReportWeight,

    // 수분 섭취량 컴포넌트
    ReportWater,

    // 소변 횟수 컴포넌트
    ReportUrineTimes,

    // 혈압 컴포넌트
    ReportBloodPressure,

    // 걸음수 컴포넌트
    ReportStep,

    // 여성 우먼(생리) 컴포넌트
    ReportMenstruation,

    // 날짜 선택 모달
    SelectDateModal: () =>
      import(
        "../../../../components/Mypage/report/ui/select-date-modal.ui.vue"
      ),

    // BMI 정보 모달
    BmiInfoModal: () =>
      import("../../../../components/Mypage/report/ui/bmi-info-modal.ui.vue"),

    // 혈압 범주 모달
    BloodPressureCategoryModal: () =>
      import(
        "../../../../components/Mypage/report/ui/blood-pressure-category-modal.ui.vue"
      ),

    // 구분선
    DivideLine,
  },

  data() {
    return {
      selectedDate: "2024-02",
      profileData: {},
      urineData: {},
      weightData: {},
      waterData: {},
      urineTimesData: {},
      bloodPressureData: {},
      stepData: {},
      menstruationData: {},
    };
  },

  mounted() {
    console.log(MockData);

    this.getReportData();
  },

  methods: {
    getReportData() {
      try {
        const data = MockData;

        console.log(data);

        this.profileData = {
          averageScore: data.averageScore,
          subject: data.subject,
        };

        this.urineData = {
          urineCount: {
            morningUrine: data.morningUrine,
            lunchUrine: data.lunchUrine,
            eveningUrine: data.eveningUrine,
          },
          urines: data.urine,
          isKetoneMode: data.ketoneMode,
        };

        this.weightData = {
          currentWeight: data.weight.value,
          targetWeight: data.subject.targetWeight,
          bmi: data.bmi,
        };

        let totalWater = 0;
        let averageWater = 0;

        if (data.water && data.water.length > 0) {
          let sum = 0;
          let len = data.water.length;

          // 시간복잡도: O(n)
          for (let i = 0; i < len; i++) {
            sum += data.water[i].value;
          }

          totalWater = sum;

          averageWater = Math.round(totalWater / len);
        }

        this.waterData = {
          targetWater: data.subject.targetWater,
          averageWater: averageWater,
          totalWater: totalWater,
        };

        this.urineTimesData = {
          urineCount: [data.morningUrine, data.lunchUrine, data.eveningUrine],
        };

        const { systolicMin, systolicMax, diastolicMin, diastolicMax } =
          getBloodPressureMinMax(data.bloodPressure);

        this.bloodPressureData = {
          systolic: [systolicMin, data.systolicMax],
          diastolic: [data.diastolicMin, data.diastolicMax],
        };

        this.stepData = {
          steps: data.step,
          targetStep: data.subject.targetStep,
        };

        this.menstruationData = {};
      } catch (error) {
        console.error(error);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
section {
  width: 100%;
  padding-bottom: 80px;
  background: #ffffff;
}
</style>
